package com.fastbee.gateway.boot.start;

import com.fastbee.common.core.mq.DeviceReportBo;
import com.fastbee.common.enums.ServerType;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.common.utils.StringUtils;
import com.fastbee.common.utils.gateway.mq.TopicsUtils;
import com.fastbee.iot.ruleEngine.MsgContext;
import com.fastbee.iot.ruleEngine.RuleProcess;
import com.fastbee.mqttclient.IEmqxMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttMessageListener;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class subscribeCallback implements IMqttMessageListener {
    @Resource
    private RuleProcess ruleProcess;
    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private IEmqxMessageProducer emqxMessageSerice;

    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) throws Exception {
        String message = new String(mqttMessage.getPayload());
        log.info("接收消息主题 : " + topic);
        log.info("接收消息Qos : " + mqttMessage.getQos());
//        log.info("接收消息内容 : " + message);

        //这里默认设备编号长度超过9位
        String[] split = topic.split("/");
        String sn;
        if (split.length == 3) {
            sn = split[2];
        } else {
            sn = Arrays.stream(split).filter(imei -> imei.length() > 9).findFirst().get();
        }

        // 规则引擎脚本处理,完成后返回结果
        List<MsgContext> contexts = ruleProcess.processRuleScript(sn, 1, topic, message);

        for (MsgContext context : contexts) {
            if (!Objects.isNull(context) && StringUtils.isNotEmpty(context.getPayload())
                    && StringUtils.isNotEmpty(context.getTopic())) {
                topic = context.getTopic();
                message = context.getPayload();
            }

            // 解析 topic 获取设备信息
            String serialNumber = topicsUtils.parseSerialNumber(topic);
            Long productId = topicsUtils.parseProductId(topic);
            String name = topicsUtils.parseTopicName(topic);

            // 构造上报数据对象
            DeviceReportBo reportBo = DeviceReportBo.builder()
                    .serialNumber(serialNumber)
                    .productId(productId)
                    .data(message.getBytes(StandardCharsets.UTF_8))
                    .platformDate(DateUtils.getNowDate())
                    .topicName(topic)
                    .serverType(ServerType.MQTT)
                    .build();

            /* 将 MQTT 的消息发送至 MQ 队列处理消息，减轻 MQTT 客户端压力 */
            emqxMessageSerice.sendEmqxMessage(name, reportBo);
        }
    }
}