package com.fastbee.data.controller.productModbus;

import com.fastbee.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

/**
 * Modbus配置寄存器Controller
 * 
 * <AUTHOR>
 * @date 2022-11-30
 */
@Api(tags = "Modbus配置寄存器")
@RestController
@RequestMapping("/modbus/config")
public class ProductModbusController extends BaseController
{

}
