package com.fastbee.device.controller;

import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.common.utils.poi.ExcelUtil;
import com.fastbee.device.domain.IotThingsModel;
import com.fastbee.device.service.IIotThingsModelService;
import com.fastbee.iot.model.ImportThingsModelInput;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.page.TableDataInfo;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 通用物模型Controller
 */
@RestController
@RequestMapping("/iot/thingsModel")
@Api(tags = "通用物模型")
public class IotThingsModelController extends BaseController {

    @Autowired
    private IIotThingsModelService iotthingsModelService;

    /**
     * 查询物模型列表
     */
    @PreAuthorize("@ss.hasPermi('iot:template:list')")
    @GetMapping("/list")
    @ApiOperation("通用物模型分页列表")
    public TableDataInfo list(IotThingsModel iotThingsModel) {
        startPage();
        return getDataTable(iotthingsModelService.selectThingsModelList(iotThingsModel));
    }

    /**
     * 导出物模型列表
     */
    @PreAuthorize("@ss.hasPermi('iot:template:export')")
    @Log(title = "导出物模型列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IotThingsModel iotThingsModel)
    {
        List<IotThingsModel> list = iotthingsModelService.selectThingsModelList(iotThingsModel);
        ExcelUtil<IotThingsModel> util = new ExcelUtil<IotThingsModel>(IotThingsModel.class);
        util.exportExcel(response, list, "物模型列表数据");
    }

    /**
     * 获取物模型详细信息
     */
    @PreAuthorize("@ss.hasPermi('iot:template:query')")
    @GetMapping(value = "/{modelId}")
    public AjaxResult getInfo(@PathVariable("modelId") Long modelId)
    {
        return success(iotthingsModelService.selectThingsModelByModelId(modelId));
    }

    /**
     * 新增物模型
     */
    @PreAuthorize("@ss.hasPermi('iot:template:add')")
    @Log(title = "新增物模型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IotThingsModel iotThingsModel)
    {
        return toAjax(iotthingsModelService.insertThingsModel(iotThingsModel));
    }

    /**
     * 修改物模型
     */
    @PreAuthorize("@ss.hasPermi('iot:template:edit')")
    @Log(title = "修改物模型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IotThingsModel iotThingsModel)
    {
        return toAjax(iotthingsModelService.updateThingsModel(iotThingsModel));
    }

    /**
     * 删除物模型
     */
    @PreAuthorize("@ss.hasPermi('iot:template:remove')")
    @Log(title = "删除物模型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{modelIds}")
    public AjaxResult remove(@PathVariable Long[] modelIds)
    {
        return toAjax(iotthingsModelService.deleteThingsModelByModelIds(modelIds));
    }

    /**
     * 获取缓存的JSON物模型
     */
    @PreAuthorize("@ss.hasPermi('iot:template:query')")
    @GetMapping(value = "/cache/{productId}")
    @ApiOperation("获取缓存的JSON物模型")
    public AjaxResult getCacheThingsModelByProductId(@PathVariable("productId") Long productId)
    {
        return AjaxResult.success("操作成功",iotthingsModelService.getCacheThingsModelByProductId(productId));
    }

    @PreAuthorize("@ss.hasPermi('iot:template:add')")
    @Log(title = "导入物模型",businessType = BusinessType.INSERT)
    @PostMapping("/import")
    @ApiOperation("导入通用物模型")
    public AjaxResult ImportByTemplateIds(@RequestBody ImportThingsModelInput input){
        int repeatCount=iotthingsModelService.importByTemplateIds(input);
        if(repeatCount==0){
            return AjaxResult.success("数据导入成功");
        }else{
            return AjaxResult.success(repeatCount+"条数据未导入，标识符重复");
        }
    }

}
