<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fastbee</groupId>
        <artifactId>fastbee-notify</artifactId>
        <version>3.8.5</version>
    </parent>

    <description>通知核心发送模块</description>
    <artifactId>fastbee-notify-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 通知配置 -->
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-notify-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fastbee</groupId>
            <artifactId>fastbee-iot-service</artifactId>
        </dependency>
        <!-- 阿里云语音 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dyvmsapi20170525</artifactId>
            <version>2.1.4</version>
        </dependency>
        <!-- 腾讯云语音 -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.952</version>
        </dependency>
        <!-- 邮箱快捷包 -->
        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-Email-core</artifactId>
            <version>3.1.0</version>
        </dependency>
        <!-- 钉钉官方包 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.1.32</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>

</project>