package com.fastbee.data.controller.productModbus;

import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.iot.domain.ModbusParams;
import com.fastbee.iot.service.IModbusParamsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 设备配置Controller
 */
@Api(tags = "设备配置")
@RestController
@RequestMapping("/modbus/params")
public class ModbusParamsController extends BaseController
{

    @Autowired
    private IModbusParamsService modbusParamsService;


    /**
     * 查询设备配置详细信息
     */
    @ApiOperation("查询设备配置详细信息")
    @PreAuthorize("@ss.hasPermi('modbus:params:query')")
    @GetMapping("/getByProductId")
    public AjaxResult getByProductId(@PathVariable("productId") Long productId)
    {
        return AjaxResult.success(modbusParamsService.selectModbusByProductId(productId));
    }

    /**
     * 新增Modbus设备配置
     */
    @ApiOperation("新增modbus设备配置")
    @PreAuthorize("@ss.hasPermi('modbus:params:add')")
    @Log(title = "modbus设备配置", businessType = BusinessType.INSERT)
    @PostMapping("/addOrUpdate")
    public AjaxResult add(@RequestBody ModbusParams modbusParams)
    {
        return AjaxResult.success(modbusParamsService.insertModbusParams(modbusParams));
    }

    /**
     * 修改Modbus设备配置
     */
    @ApiOperation("修改Modbus设备配置模板")
    @PreAuthorize("@ss.hasPermi('modbus:params:edit')")
    @Log(title = "Modbus设备配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ModbusParams modbusParams)
    {
        return toAjax(modbusParamsService.updateModbusParams(modbusParams));
    }





}
