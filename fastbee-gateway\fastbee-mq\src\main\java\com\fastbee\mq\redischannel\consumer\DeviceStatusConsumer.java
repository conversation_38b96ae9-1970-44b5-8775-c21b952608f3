package com.fastbee.mq.redischannel.consumer;

import com.fastbee.base.service.ISessionStore;
import com.fastbee.common.core.mq.DeviceStatusBo;
import com.fastbee.common.enums.DeviceStatus;
import com.fastbee.device.domain.IotDevice;
import com.fastbee.device.service.IIotDeviceService;
import com.fastbee.iot.service.cache.IDeviceCache;
import com.fastbee.iot.util.SnowflakeIdWorker;
import com.fastbee.common.core.mq.message.ReportDataBo;
import com.fastbee.mq.service.IRuleEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 设备状态消息处理类
 * <AUTHOR>
 * @date 2022/10/10 11:02
 */
@Slf4j
@Component
public class DeviceStatusConsumer {

    @Autowired
    private IDeviceCache deviceCache;
    @Resource
    private IRuleEngine ruleEngine;
    @Resource
    private IIotDeviceService deviceService;
//    @Resource
//    private IProductService productService;
    @Resource
    private ISessionStore sessionStore;
//    @Resource
//    private IMqttMessagePublish mqttMessagePublish;
    @Value("${server.broker.enabled}")
    private Boolean enabled;
    private SnowflakeIdWorker snowflakeIdWorker = new SnowflakeIdWorker(2);

    public synchronized void consume(DeviceStatusBo bo){
       try {
           IotDevice device = deviceService.selectDeviceBySerialNumber(bo.getSerialNumber());
           if (enabled){ //如果使用Netty版本
               boolean containsKey = sessionStore.containsKey(bo.getSerialNumber());
               boolean isOnline = device.getStatus() == 3;
               log.info("=>session：{},数据库：{}，更新状态:{}",containsKey,isOnline,bo.getStatus().getCode());
               if (containsKey && !isOnline){
                   //如果session存在，但数据库状态不在线，则以session为准
                  bo.setStatus(DeviceStatus.ONLINE);
               }
               if (!containsKey && isOnline){
                   bo.setStatus(DeviceStatus.OFFLINE);
               }
           }
           /*更新设备状态*/
           deviceCache.updateDeviceStatusCache(bo,device);
           //处理影子模式值
//           this.handlerShadow(device,bo.getStatus());
           //设备上下线执行规则引擎
           ReportDataBo dataBo = new ReportDataBo();
           dataBo.setRuleEngine(true);
           dataBo.setProductId(device.getProductId());
           dataBo.setType(bo.getStatus().equals(DeviceStatus.ONLINE)? 5 : 6);
           dataBo.setSerialNumber(bo.getSerialNumber());
           ruleEngine.ruleMatch(dataBo);
       }catch (Exception e){
           log.error("=>设备状态处理异常",e);
       }
    }

//    private void handlerShadow(IotDevice device,DeviceStatus status){
//        //获取设备协议编码
//        Product product = productService.selectProductByProductId(device.getProductId());
//        /* 设备上线 处理影子值*/
//        if (status.equals(DeviceStatus.ONLINE)){
//            ThingsModelShadow shadow = deviceService.getDeviceShadowThingsModel(device);
//            List<ThingsModelSimpleItem> properties = shadow.getProperties();
//            List<ThingsModelSimpleItem> functions = shadow.getFunctions();
//            //JsonArray组合发送
//            if (FastBeeConstant.PROTOCOL.JsonArray.equals(product.getProtocolCode())) {
//                if (!CollectionUtils.isEmpty(properties)) {
//                    mqttMessagePublish.publishProperty(device.getProductId(), device.getSerialNumber(), properties, 3);
//                }
//                if (!CollectionUtils.isEmpty(functions)) {
//                    mqttMessagePublish.publishFunction(device.getProductId(), device.getSerialNumber(), functions, 3);
//                }
//            } else { //其他协议单个发送
//                functions.addAll(properties);
//                if (!CollectionUtils.isEmpty(functions)) {
//                    for (ThingsModelSimpleItem function : functions) {
//                        MQSendMessageBo bo = new MQSendMessageBo();
//                        bo.setTransport(product.getTransport());
//                        bo.setProtocolCode(product.getProtocolCode());
//                        bo.setIsShadow(false);
//                        bo.setProductId(product.getProductId());
//                        bo.setIdentifier(function.getId());
//                        bo.setSerialNumber(device.getSerialNumber());
//                        bo.setType(ThingsModelType.SERVICE);
//                        JSONObject jsonObject = new JSONObject();
//                        jsonObject.put(function.getId(),function.getValue());
//                        bo.setValue(jsonObject);
//                        long id = snowflakeIdWorker.nextId();
//                        bo.setMessageId(id +"");
//                        bo.setSlaveId(function.getSlaveId());
//                        //发送到MQ处理
//                        MessageProducer.sendFunctionInvoke(bo);
//                    }
//                }
//            }
//        }
//    }
}
